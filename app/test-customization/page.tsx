'use client';

import React, { useState } from 'react';
import { ISchoolResponse } from '@/apis/schoolApi';
import { 
  assessSchoolCustomization, 
  SchoolCustomizationLevel 
} from '@/utils/schoolCustomization';
import { SchoolCustomizationPrompt } from '@/components/molecules/SchoolCustomizationPrompt/SchoolCustomizationPrompt';
import { SchoolCustomizationBanner } from '@/components/molecules/SchoolCustomizationBanner/SchoolCustomizationBanner';

// Mock school data for testing different states
const mockBasicSchool: ISchoolResponse = {
  id: '1',
  name: "<PERSON>'s School",
  address: 'Not specified',
  phoneNumber: '',
  registeredNumber: '',
  email: '<EMAIL>'
};

const mockPartialSchool: ISchoolResponse = {
  id: '2',
  name: 'Sunshine Academy',
  address: '123 Education Street',
  phoneNumber: '******-0123',
  registeredNumber: '',
  email: '<EMAIL>',
  brand: {
    id: 'brand1',
    color: '#3B82F6'
  }
};

const mockFullyCustomizedSchool: ISchoolResponse = {
  id: '3',
  name: 'Elite Learning Institute',
  address: '456 Knowledge Avenue, Education City',
  phoneNumber: '******-0456',
  registeredNumber: 'EDU-2024-001',
  email: '<EMAIL>',
  brand: {
    id: 'brand2',
    logo: 'https://via.placeholder.com/64x64/3B82F6/FFFFFF?text=ELI',
    color: '#3B82F6',
    image: 'https://via.placeholder.com/800x200/10B981/FFFFFF?text=Elite+Learning'
  }
};

const mockUser = {
  name: 'John Doe',
  email: '<EMAIL>'
};

export default function TestCustomizationPage() {
  const [selectedSchool, setSelectedSchool] = useState<'basic' | 'partial' | 'full'>('basic');

  const getSchoolData = () => {
    switch (selectedSchool) {
      case 'basic':
        return mockBasicSchool;
      case 'partial':
        return mockPartialSchool;
      case 'full':
        return mockFullyCustomizedSchool;
      default:
        return mockBasicSchool;
    }
  };

  const schoolData = getSchoolData();
  const assessment = assessSchoolCustomization(schoolData, mockUser.name, mockUser.email);

  const handleCustomizeClick = () => {
    alert('Would navigate to /school-customization');
  };

  return (
    <div className="min-h-screen bg-slate-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h1 className="text-3xl font-bold mb-4">School Customization UI Test</h1>
          <p className="text-gray-600 mb-6">
            This page demonstrates how the UI changes based on school customization level.
          </p>

          {/* School Type Selector */}
          <div className="flex gap-4 mb-6">
            <button
              className={`btn ${selectedSchool === 'basic' ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => setSelectedSchool('basic')}
            >
              Basic School
            </button>
            <button
              className={`btn ${selectedSchool === 'partial' ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => setSelectedSchool('partial')}
            >
              Partially Customized
            </button>
            <button
              className={`btn ${selectedSchool === 'full' ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => setSelectedSchool('full')}
            >
              Fully Customized
            </button>
          </div>

          {/* Assessment Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold mb-2">Assessment Results:</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium">Level:</span> {assessment.level}
              </div>
              <div>
                <span className="font-medium">Score:</span> {assessment.score}%
              </div>
              <div>
                <span className="font-medium">Missing:</span> {assessment.missingCustomizations.length} items
              </div>
            </div>
          </div>
        </div>

        {/* Banner Demo */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Customization Banner</h2>
          <SchoolCustomizationBanner
            assessment={assessment}
            schoolName={schoolData.name}
            onCustomizeClick={handleCustomizeClick}
          />
        </div>

        {/* Prompt Demo */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Customization Prompt</h2>
          <SchoolCustomizationPrompt
            assessment={assessment}
            schoolName={schoolData.name}
            onCustomizeClick={handleCustomizeClick}
          />
        </div>

        {/* School Data Display */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Current School Data</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Name:</span> {schoolData.name}
            </div>
            <div>
              <span className="font-medium">Address:</span> {schoolData.address}
            </div>
            <div>
              <span className="font-medium">Phone:</span> {schoolData.phoneNumber || 'Not set'}
            </div>
            <div>
              <span className="font-medium">Email:</span> {schoolData.email}
            </div>
            <div>
              <span className="font-medium">Registration:</span> {schoolData.registeredNumber || 'Not set'}
            </div>
            <div>
              <span className="font-medium">Brand:</span> {schoolData.brand ? 'Yes' : 'No'}
            </div>
          </div>

          {schoolData.brand && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium mb-2">Brand Details:</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Logo:</span> {schoolData.brand.logo ? 'Yes' : 'No'}
                </div>
                <div>
                  <span className="font-medium">Color:</span> {schoolData.brand.color || 'Not set'}
                </div>
                <div>
                  <span className="font-medium">Image:</span> {schoolData.brand.image ? 'Yes' : 'No'}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Recommendations */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Recommendations</h2>
          {assessment.recommendations.length > 0 ? (
            <ul className="list-disc list-inside space-y-2 text-sm">
              {assessment.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          ) : (
            <p className="text-green-600">🎉 Your school is fully customized!</p>
          )}
        </div>
      </div>
    </div>
  );
}
