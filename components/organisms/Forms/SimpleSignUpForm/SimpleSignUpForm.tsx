'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { Mail, Lock, User } from 'lucide-react';

import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { signUpAction } from '@/actions/user.action';
import { autoCreateSchoolForUser } from '@/actions/school.action';
import { cn } from '@/utils/cn';

import { simpleSignUpSchema, SimpleSignUpFormData } from './SimpleSignUpForm.schema';

export default function SimpleSignUpForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<'signup' | 'creating-school' | 'complete'>('signup');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SimpleSignUpFormData>({
    resolver: zodResolver(simpleSignUpSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const getButtonText = () => {
    switch (currentStep) {
      case 'signup':
        return 'Creating Account...';
      case 'creating-school':
        return 'Setting up your school...';
      case 'complete':
        return 'Complete! Redirecting...';
      default:
        return 'Creating Account...';
    }
  };

  const getButtonLabel = () => {
    switch (currentStep) {
      case 'signup':
        return 'Creating account...';
      case 'creating-school':
        return 'Setting up your school...';
      case 'complete':
        return 'Complete! Redirecting...';
      default:
        return 'Create account';
    }
  };

  const onSubmit = async (data: SimpleSignUpFormData) => {
    try {
      setIsLoading(true);
      setError(null);
      setCurrentStep('signup');

      // Step 1: Create user account
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('email', data.email);
      formData.append('password', data.password);

      const result = await signUpAction(formData);

      if (result.status === 'error') {
        setError(typeof result.message === 'string' ? result.message : 'Failed to create account');
        return;
      }

      // Step 2: Auto sign-in after successful registration
      const signInResponse = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (signInResponse?.error) {
        setError('Account created successfully, but auto sign-in failed. Please sign in manually.');
        setTimeout(() => {
          router.push('/auth/sign-in');
        }, 3000);
        return;
      }

      // Step 4: Success - redirect to dashboard
      setCurrentStep('complete');
      setTimeout(() => {
        router.push('/');
        router.refresh();
      }, 1500);

    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred');
      setCurrentStep('signup');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="space-y-6">
          {/* Error Alert */}
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AlertMessage
                type="error"
                message={error}
              />
            </motion.div>
          )}

          {/* Main Sign Up Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="relative">
              {/* Gradient Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-2xl"></div>
              
              {/* Glass Effect */}
              <div className="relative bg-white/70 backdrop-blur-sm border border-white/40 rounded-2xl p-8 shadow-lg">
                {/* Header */}
                <div className="text-center mb-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                  >
                    <h1 className="text-3xl font-bold text-gray-800 mb-2">
                      Create Your Account
                    </h1>
                    <p className="text-gray-600">
                      Transform your teaching with AI-powered worksheets
                    </p>
                  </motion.div>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" role="form" aria-label="Sign up form">
                  {/* Name Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <FormField
                      label="Full Name"
                      error={errors.name?.message}
                      required
                    >
                      <InputWithIcon
                        type="text"
                        placeholder="Enter your full name"
                        leftIcon={<User size={18} className="text-blue-500" />}
                        className={cn(
                          'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                          'bg-white/50 backdrop-blur-sm',
                          errors.name
                            ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                            : 'border-gray-200 focus:border-blue-500'
                        )}
                        {...register('name')}
                      />
                    </FormField>
                  </motion.div>

                  {/* Email Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <FormField
                      label="Email Address"
                      error={errors.email?.message}
                      required
                    >
                      <InputWithIcon
                        type="email"
                        placeholder="Enter your email address"
                        leftIcon={<Mail size={18} className="text-blue-500" />}
                        className={cn(
                          'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                          'bg-white/50 backdrop-blur-sm',
                          errors.email
                            ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                            : 'border-gray-200 focus:border-blue-500'
                        )}
                        {...register('email')}
                      />
                    </FormField>
                  </motion.div>

                  {/* Password Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    <FormField
                      label="Password"
                      error={errors.password?.message}
                      required
                    >
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                          <Lock size={18} className="text-blue-500" />
                        </div>
                        <PasswordInput
                          placeholder="Enter your password (min. 8 characters)"
                          hasLeftIcon={true}
                          className={cn(
                            'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                            'bg-white/50 backdrop-blur-sm',
                            errors.password
                              ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                              : 'border-gray-200 focus:border-blue-500'
                          )}
                          {...register('password')}
                        />
                      </div>
                    </FormField>
                  </motion.div>

                  {/* Confirm Password Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.65 }}
                  >
                    <FormField
                      label="Confirm Password"
                      error={errors.confirmPassword?.message}
                      required
                    >
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                          <Lock size={18} className="text-blue-500" />
                        </div>
                        <PasswordInput
                          placeholder="Confirm your password"
                          hasLeftIcon={true}
                          className={cn(
                            'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                            'bg-white/50 backdrop-blur-sm',
                            errors.confirmPassword
                              ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                              : 'border-gray-200 focus:border-blue-500'
                          )}
                          {...register('confirmPassword')}
                        />
                      </div>
                    </FormField>
                  </motion.div>

                  {/* Submit Button */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.75 }}
                  >
                    <Button
                      type="submit"
                      variant="primary"
                      className="w-full h-12 text-base font-semibold rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl focus:ring-4 focus:ring-blue-200 focus:ring-offset-2 border-0"
                      isLoading={isLoading}
                      disabled={isLoading}
                      aria-label={getButtonLabel()}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center space-x-2">
                          <span className="loading loading-spinner loading-sm"></span>
                          <span>{getButtonText()}</span>
                        </div>
                      ) : (
                        'Create Account'
                      )}
                    </Button>
                  </motion.div>
                </form>
              </div>
            </div>
          </motion.div>

          {/* Navigation to Sign In */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.85 }}
          >
            <div className="text-center pt-6 border-t border-gray-200">
              <p className="text-gray-600">
                Already have an account?{' '}
                <Link
                  href="/auth/sign-in"
                  className="text-blue-600 hover:text-blue-700 font-medium transition-colors"
                >
                  Sign in here
                </Link>
              </p>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
